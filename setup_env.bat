@echo off
echo 🔧 寸止环境变量设置脚本
echo ================================

set "CUNZHI_PATH=%~dp0target\release"
echo 当前项目路径: %CUNZHI_PATH%

echo.
echo 📋 检查可执行文件...
if exist "%CUNZHI_PATH%\寸止.exe" (
    echo ✅ 找到 寸止.exe
) else (
    echo ❌ 未找到 寸止.exe
    echo 请先编译项目: cargo tauri build
    pause
    exit /b 1
)

if exist "%CUNZHI_PATH%\等一下.exe" (
    echo ✅ 找到 等一下.exe
) else (
    echo ❌ 未找到 等一下.exe
    echo 请先编译项目: cargo tauri build
    pause
    exit /b 1
)

echo.
echo 🎯 设置环境变量...

REM 检查是否已经在PATH中
echo %PATH% | findstr /i "%CUNZHI_PATH%" >nul
if %errorlevel% equ 0 (
    echo ⚠️  路径已存在于 PATH 中
) else (
    echo 📝 添加到用户 PATH 环境变量...
    
    REM 使用 PowerShell 设置用户环境变量
    powershell -Command "[Environment]::SetEnvironmentVariable('Path', [Environment]::GetEnvironmentVariable('Path', 'User') + ';%CUNZHI_PATH%', 'User')"
    
    if %errorlevel% equ 0 (
        echo ✅ 环境变量设置成功
    ) else (
        echo ❌ 环境变量设置失败
        echo 💡 请手动添加以下路径到系统 PATH:
        echo    %CUNZHI_PATH%
        pause
        exit /b 1
    )
)

echo.
echo 🧪 测试命令...
echo 临时设置 PATH 进行测试...
set "PATH=%PATH%;%CUNZHI_PATH%"

echo 测试 寸止 命令:
寸止 --help 2>nul
if %errorlevel% equ 0 (
    echo ✅ 寸止 命令可用
) else (
    echo ⚠️  寸止 命令测试（可能正常，某些程序不支持 --help）
)

echo.
echo 测试 等一下 命令:
等一下 --help 2>nul
if %errorlevel% equ 0 (
    echo ✅ 等一下 命令可用
) else (
    echo ⚠️  等一下 命令测试（可能正常，某些程序不支持 --help）
)

echo.
echo 🎉 设置完成！
echo.
echo 📋 使用方法:
echo   启动 MCP 服务器: 寸止
echo   启动设置界面:   等一下
echo   测试弹窗:       等一下 --mcp-request test_simple_popup.json
echo.
echo ⚠️  注意: 请重新打开命令提示符或重启电脑以使环境变量生效
echo.

pause
